package com.morningstar.dataac.morningstar.data.timeseries.service;

import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.FixedIncomeDataPoint;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

public interface TsService {
    Flux<Result> getData(MartRequest martRequest, List<String> idList, List<FixedIncomeDataPoint> dataPoints, Map<String, String> columnDpMap);
}
