package com.morningstar.dataac.martgateway.core.common.entity.result;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.T;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.dataac.martgateway.core.common.util.ResultUtil;
import com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import reactor.util.Logger;
import reactor.util.Loggers;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TsResult extends Result {

    private Map<String, TsDataProtoBuf.TSDataDouble> values;
    private static final Logger LOGGER = Loggers.getLogger(TsResult.class);

    public TsResult(String id, Map<String, TsDataProtoBuf.TSDataDouble> values) {
        this.id = id;
        this.values = values;
    }

    /**
     * @return data points of retrieve result
     */
    @Override
    public Map<String, TsDataProtoBuf.TSDataDouble> getValues() {
        return values;
    }

    @Override
    public Map getPrimaryKeys() {
        return Map.of();
    }

    /**
     * @return
     */
    public List<T> transform() {
        if (MapUtils.isEmpty(this.getValues())) {
            return Arrays.asList(new T("404", new ArrayList<>()));
        }
        return null;

//        return this.getValues().entrySet().stream()
//                .map(entry -> new T(entry.getKey(), entry.getValue())).collect(Collectors.toList());
    }

    @Override
    public void updateValues(String dataPointId, Function<String, String> function) {
    }

    @Override
    public void renameDataPoint(String originalDataPointId, String targetDataPointId) {
        if (MapUtils.isNotEmpty(values) && values.containsKey(originalDataPointId)) {
            TsDataProtoBuf.TSDataDouble value = values.get(originalDataPointId);
            values.remove(originalDataPointId);
            values.put(targetDataPointId, value);
        }
    }

    @Override
    public void duplicateContent(String originalDataPointId, String targetDataPointId) {
        if (MapUtils.isNotEmpty(values) && values.containsKey(originalDataPointId)) {
            values.put(targetDataPointId, values.get(originalDataPointId));
        }
    }
}
