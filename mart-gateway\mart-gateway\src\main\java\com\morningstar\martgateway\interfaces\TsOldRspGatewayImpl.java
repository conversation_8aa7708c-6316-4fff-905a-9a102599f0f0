package com.morningstar.martgateway.interfaces;

import com.morningstar.dataac.martgateway.service.MartGateway;
import com.morningstar.dataac.martgateway.core.common.entity.result.DataPointError;
import com.morningstar.dataac.martgateway.core.common.entity.result.ErrorResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.TimeSeriesResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.IdMapper;
import com.morningstar.dataac.martgateway.core.common.entity.result.response.V;
import com.morningstar.martgateway.applications.tscacheproxy.TsCacheProxyApplication;
import com.morningstar.dataac.martgateway.core.entitlement.entity.CachedEntitlement;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.martgateway.domains.core.pipeline.MartDataPipeline;
import com.morningstar.dataac.martgateway.core.entitlement.entity.FilteredRequestData;
import com.morningstar.dataac.martgateway.core.entitlement.service.DataEntitlementService;
import com.morningstar.dataac.martgateway.core.entitlement.service.EntitlementRequestFilterService;
import com.morningstar.dataac.martgateway.core.entitlement.service.requestwrapper.EntitlementMartRequestWrapper;
import com.morningstar.martgateway.domains.rdb.helper.ExchangeRateCache;
import com.morningstar.martgateway.domains.timeseries.entity.TSData;
import com.morningstar.martgateway.domains.timeseries.entity.TSItem;
import com.morningstar.martgateway.domains.timeseries.entity.TSRequest;
import com.morningstar.martgateway.domains.timeseries.entity.TSResponse;
import com.morningstar.martgateway.domains.timeseries.entity.TSStatus;
import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas;
import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData;
import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.util.IdMapUtil;
import com.morningstar.dataac.martgateway.core.common.util.SchedulerConfiguration;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
public class TsOldRspGatewayImpl extends AbstractTsApiProxy implements MartGateway<TSResponse, MartRequest> {

    private DataEntitlementService<MartRequest> dataEntitlementService;
    private EntitlementRequestFilterService<MartRequest, EntitlementMartRequestWrapper> entitlementMartRequestFilterService;

    private ExchangeRateCache exchangeRateCache;

    public TsOldRspGatewayImpl(MartDataPipeline martDataPipeline, TsCacheProxyApplication tsCacheProxyApplication, IdMapUtil idMapUtil,
                               DataEntitlementService<MartRequest> dataEntitlementService,
                               EntitlementRequestFilterService<MartRequest, EntitlementMartRequestWrapper> entitlementMartRequestFilterService,
                               ExchangeRateCache exchangeRateCache) {
        super(martDataPipeline, tsCacheProxyApplication, idMapUtil);
        this.dataEntitlementService = dataEntitlementService;
        this.entitlementMartRequestFilterService = entitlementMartRequestFilterService;
        this.exchangeRateCache = exchangeRateCache;
    }

    @Override
    public Mono<TSResponse> asyncRetrieveSecurities(MartRequest martRequest) {
        if (CollectionUtils.isEmpty(martRequest.getDps()) || CollectionUtils.isEmpty(martRequest.getIds())) {
            return Mono.empty();
        }

        martRequest.setIdMappers(this.idMapUtil.getIdMappers(martRequest.getListOfIds()));
        MartRequest originalMartRequest = martRequest.shallowCopy();
        Map<String, IdMapper> idMapperById = originalMartRequest.getIdMappers().stream().collect(Collectors.toMap(IdMapper::getInvestmentId, v -> v)); //use originalMartRequest so that even filtered IDs' id mappers can still be referenced.

        Flux<Result> exchangeRateData = exchangeRateCache.getExchangeRatesData(martRequest);
        if(CollectionUtils.isEmpty(martRequest.getIds())) {
            return buildTimeSeriesData(exchangeRateData, idMapperById)
                    .subscribeOn(SchedulerConfiguration.getScheduler());
        }

        CachedEntitlement cachedEntitlement = null;
        boolean checkEntitlement = martRequest.isCheckEntitlement();
        FilteredRequestData<MartRequest> filteredRequestData = null;
        EntitlementMartRequestWrapper entitlementMartRequestWrapper = new EntitlementMartRequestWrapper(martRequest);
        if (checkEntitlement) {
            cachedEntitlement = dataEntitlementService.getEntitlement(martRequest.getUserId(), martRequest.getConfigId());

            filteredRequestData = entitlementMartRequestFilterService.filterRequest(
                    cachedEntitlement,
                    entitlementMartRequestWrapper);
            martRequest = filteredRequestData.getRequest();
        }

        TSRequest tsRequest = getTsCacheRequest(martRequest);
        Map<String, String> secIdRequestedIdMap = getTsSecIdMapAndPrepareRequests(martRequest, tsRequest);
        Flux<Result> tsData = getInputTsIdResult(getTsData(tsRequest), secIdRequestedIdMap);
        Flux<Result> rdbData = martDataPipeLine.execute(martRequest);

        Flux<Result> filteredResults;
        if (checkEntitlement) {
            filteredResults = Flux.fromIterable(filteredRequestData.getFilteredResults());
        } else {
            filteredResults = Flux.empty();
        }

        Flux<Result> mergedResultFlux = Flux.merge(rdbData, tsData, filteredResults, exchangeRateData);

        if (checkEntitlement) {
            mergedResultFlux = dataEntitlementService.applyDataEntitlement(mergedResultFlux, cachedEntitlement, new EntitlementMartRequestWrapper(originalMartRequest));
        }

        return buildTimeSeriesData(mergedResultFlux, idMapperById)
                .subscribeOn(SchedulerConfiguration.getScheduler());
    }

    @Override
    public TSResponse syncRetrieveSecurities(MartRequest martRequest) {
        return asyncRetrieveSecurities(martRequest).block();
    }



    private Pair<List<TSItem>, Status> buildTsItemsFromResult(List<Result> results, Map<String, IdMapper> idMapperById) {
        List<TSItem> tsItemList = new ArrayList<>();
        Status status = Status.OK;
        for(Result result: results) {
            if (result instanceof TimeSeriesResult) {
                Pair<List<TSItem>, Status> tsItemsAndErrorFlag = buildTsItemsFromTimeSeriesErrorResult((TimeSeriesResult) result, idMapperById);
                tsItemList.addAll(tsItemsAndErrorFlag.getLeft());
                if(status == Status.OK){
                    status = tsItemsAndErrorFlag.getRight();
                }
            } else if (result instanceof ErrorResult) {
                tsItemList.add(buildTsItemFromErrorResult((ErrorResult) result, idMapperById));
                status = Status.SUCCESS_WITH_DP_ERRORS;
            }
        }
        return Pair.of(tsItemList,status);
    }

    private Pair<TimeSeriesDatas, Status> buildTimeSeriesDataFromResult(List<Result> results, Map<String, IdMapper> idMapperById) {
        TimeSeriesDatas.Builder timeSeriesDataBuilder = TimeSeriesDatas.newBuilder();
        Status status = Status.OK;

        for(Result result: results) {
            if (result instanceof TimeSeriesResult) {
                Pair<List<TimeSeriesData>, Status> timeSeriesDataAndErrorFlag = buildTimeSeriesDataFromTimeSeriesResult((TimeSeriesResult) result, idMapperById);
                timeSeriesDataBuilder.addAllValues(timeSeriesDataAndErrorFlag.getLeft());
                if(status == Status.OK){
                    status = timeSeriesDataAndErrorFlag.getRight();
                }
            } else if (result instanceof ErrorResult) {
                timeSeriesDataBuilder.addValues(buildTimeSeriesDataFromErrorResult((ErrorResult) result, idMapperById));
                status = Status.SUCCESS_WITH_DP_ERRORS;
            }
        }

        // Set status code and message
        timeSeriesDataBuilder.setRetcode(Long.parseLong(status.getCode()));
        timeSeriesDataBuilder.setMsg(status.getMessage());

        return Pair.of(timeSeriesDataBuilder.build(), status);
    }

    private TSItem buildTsItemFromErrorResult(ErrorResult errorResult, Map<String, IdMapper> idMapperById){
        TSItem tsItem = new TSItem();
        tsItem.setSecid(buildSecId(errorResult.getId(), idMapperById));
        tsItem.setDataid(errorResult.getDatapointId());
        tsItem.setErrorcode(errorResult.getErrorCode());
        return tsItem;
    }

    private Pair<List<TSItem>, Status> buildTsItemsFromTimeSeriesErrorResult(TimeSeriesResult tsResult, Map<String, IdMapper> idMapperById){
        List<TSItem> tsItemList = new ArrayList<>();
        Status status = Status.OK;
        String secId = buildSecId(tsResult.getId(), idMapperById);
        if(CollectionUtils.isNotEmpty(tsResult.getErrors())) {
            for (DataPointError error : tsResult.getErrors()) {
                TSItem tsItem = new TSItem();
                tsItem.setSecid(secId);
                tsItem.setDataid(error.getDatapointId());
                tsItem.setErrorcode(error.getErrorCode());
                tsItemList.add(tsItem);
            }
            status = Status.SUCCESS_WITH_DP_ERRORS;
        }
        else {
            try {
                for (Entry<String, List<V>> tsValuesForDp : tsResult.getValues().entrySet()) {
                    TSItem tsItem = new TSItem();
                    tsItem.setDataid(tsValuesForDp.getKey());
                    tsItem.setSecid(secId);
                    List<TSData> tsDataList = buildTsData(tsValuesForDp);
                    tsItem.setData(tsDataList);
                    tsItemList.add(tsItem);
                }
            } catch (NumberFormatException e){
                status = Status.INVALID_DATATYPE_IN_DATAPOINT_FOR_TS;
            }
        }
        return Pair.of(tsItemList, status);
    }

    private List<TSData> buildTsData(Entry<String, List<V>> tsValues) {
        if(tsValues == null || tsValues.getValue() == null){
            return Collections.emptyList();
        }
        return tsValues.getValue().stream()
                .map(v -> {
                    TSData tsData = new TSData();
                    tsData.setDate(formatDate(v.getI()));
                    if(StringUtils.isNotEmpty(v.getV())) {
                        tsData.setValue(Double.parseDouble(v.getV()));
                    }
                    return tsData;
                })
                .collect(Collectors.toList());
    }

    private TimeSeriesData buildTimeSeriesDataFromErrorResult(ErrorResult errorResult, Map<String, IdMapper> idMapperById) {
        String secId = buildSecId(errorResult.getId(), idMapperById);
        String[] secIdWithUniverse = secId.split(";");
        String secIdPart = secIdWithUniverse[0];
        String universe = (secIdWithUniverse.length >= 2) ? secIdWithUniverse[1] : null;

        TimeSeriesData.Builder dataBuilder = TimeSeriesData.newBuilder()
                .setSecId(secIdPart)
                .setDataId(errorResult.getDatapointId())
                .setErrorCode(errorResult.getErrorCode());

        if (universe != null) {
            dataBuilder.setUniverse(universe);
        }

        return dataBuilder.build();
    }

    private Pair<List<TimeSeriesData>, Status> buildTimeSeriesDataFromTimeSeriesResult(TimeSeriesResult tsResult, Map<String, IdMapper> idMapperById) {
        List<TimeSeriesData> timeSeriesDataList = new ArrayList<>();
        Status status = Status.OK;
        String secId = buildSecId(tsResult.getId(), idMapperById);
        String[] secIdWithUniverse = secId.split(";");
        String secIdPart = secIdWithUniverse[0];
        String universe = (secIdWithUniverse.length >= 2) ? secIdWithUniverse[1] : null;

        if(CollectionUtils.isNotEmpty(tsResult.getErrors())) {
            for (DataPointError error : tsResult.getErrors()) {
                TimeSeriesData.Builder dataBuilder = TimeSeriesData.newBuilder()
                        .setSecId(secIdPart)
                        .setDataId(error.getDatapointId())
                        .setErrorCode(error.getErrorCode());

                if (universe != null) {
                    dataBuilder.setUniverse(universe);
                }

                timeSeriesDataList.add(dataBuilder.build());
            }
            status = Status.SUCCESS_WITH_DP_ERRORS;
        }
        else {
            try {
                for (Entry<String, List<V>> tsValuesForDp : tsResult.getValues().entrySet()) {
                    TimeSeriesData.Builder dataBuilder = TimeSeriesData.newBuilder()
                            .setSecId(secIdPart)
                            .setDataId(tsValuesForDp.getKey());

                    if (universe != null) {
                        dataBuilder.setUniverse(universe);
                    }

                    List<TSValuePair> tsValuePairs = buildTsValuePairs(tsValuesForDp);
                    dataBuilder.addAllValues(tsValuePairs);
                    timeSeriesDataList.add(dataBuilder.build());
                }
            } catch (NumberFormatException e){
                status = Status.INVALID_DATATYPE_IN_DATAPOINT_FOR_TS;
            }
        }
        return Pair.of(timeSeriesDataList, status);
    }

    private List<TSValuePair> buildTsValuePairs(Entry<String, List<V>> tsValues) {
        if(tsValues == null || tsValues.getValue() == null){
            return Collections.emptyList();
        }
        return tsValues.getValue().stream()
                .map(v -> {
                    TSValuePair.Builder pairBuilder = TSValuePair.newBuilder()
                            .addDates(formatDate(v.getI()));
                    if(StringUtils.isNotEmpty(v.getV())) {
                        pairBuilder.addValues(Double.parseDouble(v.getV()));
                    }
                    return pairBuilder.build();
                })
                .collect(Collectors.toList());
    }

    private Mono<TSResponse> buildTimeSeriesData(Flux<Result> results, Map<String, IdMapper> idMapperById){
        return results.collectList()
                .map(resultList -> {
                    Pair<TimeSeriesDatas, Status> timeSeriesDataAndErrorFlag = buildTimeSeriesDataFromResult(resultList, idMapperById);
                    TimeSeriesDatas timeSeriesDatas = timeSeriesDataAndErrorFlag.getLeft();
                    Status errorStatus = timeSeriesDataAndErrorFlag.getRight();

                    TSResponse tsResponse = new TSResponse();
                    TSStatus status = new TSStatus();
                    tsResponse.setStatus(status);
                    status.setMsg("");
                    status.setCode("0");
                    if(errorStatus != null && errorStatus != Status.OK){
                        status.setCode(errorStatus.getCode());
                        status.setMsg(errorStatus.getMessage());
                    }

                    // Set the TimeSeriesDatas for direct protobuf conversion
                    tsResponse.setTimeSeriesDatas(timeSeriesDatas);

                    return tsResponse;
                });
    }



    private int formatDate(String date) {
        return (int) (LocalDate.parse(date, dateTimeFormatter).toEpochDay() - START_EPOCH_DAY);
    }
}
