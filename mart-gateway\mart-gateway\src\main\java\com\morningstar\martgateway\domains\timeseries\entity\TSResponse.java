package com.morningstar.martgateway.domains.timeseries.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.morningstar.dataac.martgateway.core.common.entity.Status;
import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair;
import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData;
import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
public class TSResponse {
    @JsonProperty("s")
    private TSStatus status;
    @JsonProperty("c")
    private TSContent content;

    // Field to store TimeSeriesDatas for direct protobuf conversion
    private TimeSeriesDatas timeSeriesDatas;

    public TSResponse(TSStatus status){
        this.status = status;
        this.content = null;
    }

    public TSResponse(TimeSeriesDatas timeSeriesDatas) {
        this.timeSeriesDatas = timeSeriesDatas;
        // Convert TimeSeriesDatas to TSResponse fields for JSON compatibility
        this.status = new TSStatus();
        this.status.setCode(String.valueOf(timeSeriesDatas.getRetcode()));
        this.status.setMsg(timeSeriesDatas.getMsg());

        this.content = new TSContent();
        List<TSItem> tsItems = new LinkedList<>();
        for (TimeSeriesData timeSeriesData : timeSeriesDatas.getValuesList()) {
            TSItem tsItem = new TSItem();
            tsItem.setDataid(timeSeriesData.getDataId());
            tsItem.setSecid(timeSeriesData.getSecId() + ";" + timeSeriesData.getUniverse());
            tsItem.setErrorcode(timeSeriesData.getErrorCode());
            List<TSData> dataList = timeSeriesData.getValuesList().stream().map(v -> {
                TSData tsData = new TSData();
                tsData.setDate((int) v.getDates(0));
                tsData.setValue(v.getValues(0));
                return tsData;
            }).collect(Collectors.toList());
            tsItem.setData(dataList);
            tsItems.add(tsItem);
        }
        this.content.setItems(tsItems);
    }

    public static TSResponse fromProtobuf(TimeSeriesDatas data) {
        TSResponse response = new TSResponse();
        TSStatus status = new TSStatus();
        status.setCode(String.valueOf(data.getRetcode()));
        status.setMsg(data.getMsg());
        response.setStatus(status);

        TSContent content = new TSContent();
        List<TSItem> tsItems = new LinkedList<>();
        for (TimeSeriesData timeSeriesData : data.getValuesList()) {
            TSItem tsItem = new TSItem();
            tsItem.setDataid(timeSeriesData.getDataId());
            tsItem.setSecid(timeSeriesData.getSecId() + ";" + timeSeriesData.getUniverse());
            tsItem.setErrorcode(timeSeriesData.getErrorCode());
            List<TSData> dataList = timeSeriesData.getValuesList().stream().map(v -> {
                TSData tsData = new TSData();
                tsData.setDate((int) v.getDates(0));
                tsData.setValue(v.getValues(0));
                return tsData;
            }).collect(Collectors.toList());
            tsItem.setData(dataList);
            tsItems.add(tsItem);
        }
        content.setItems(tsItems);
        response.setContent(content);
        return response;
    }

    public TimeSeriesDatas toProtobuf() {
        // If TimeSeriesDatas is already available, return it directly for performance
        if (timeSeriesDatas != null) {
            return timeSeriesDatas;
        }

        // Otherwise, build from TSResponse fields (for backward compatibility)
        TimeSeriesDatas.Builder timeSeriesDataBuilder = TimeSeriesDatas.newBuilder()
                .setRetcode(Long.parseLong(getStatus().getCode()))
                .setMsg(getStatus().getMsg());

        if(getContent() != null) { //some TSResponse will only have status values (i.e. error codes) with no content values
            for (TSItem item : getContent().getItems()) {
                String[] secIdWithUniverse = item.getSecid().split(";");
                String secId = secIdWithUniverse[0];
                String universe = (secIdWithUniverse.length >= 2) ? secIdWithUniverse[1] : null;
                TimeSeriesData.Builder data = TimeSeriesData.newBuilder()
                        .setSecId(secId)
                        .setDataId(item.getDataid());
                if(universe != null){
                    data.setUniverse(universe);
                }
                if (item.getErrorcode() != null) {
                    data.setErrorCode(item.getErrorcode());
                }
                if (item.getData() != null) {
                    List<TSValuePair> tsValuePairs = item.getData().stream()
                            .map(tsData -> TSValuePair.newBuilder()
                                    .addDates(tsData.getDate())
                                    .addValues(tsData.getValue())
                                    .build())
                            .collect(Collectors.toList());
                    data.addAllValues(tsValuePairs);
                }
                timeSeriesDataBuilder.addValues(data);
            }
        }
        return timeSeriesDataBuilder.build();
    }

    public static TSResponse buildErrorResponse(Status status){
        TSStatus tsStatus = new TSStatus(status.getCode(), status.getMessage());
        return new TSResponse(tsStatus);
    }

    public static TSResponse toJson(TimeSeriesDatas timeSeriesDatas) {
        return new TSResponse(timeSeriesDatas);
    }
}
