package com.morningstar.dataac.morningstar.data.timeseries.service.gateway;

import com.morningstar.dataac.martgateway.core.common.entity.log.LogAttribute;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.TsResult;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf;
import com.morningstar.dataac.martgateway.service.Gateway;
import com.morningstar.dataac.morningstar.data.timeseries.service.TsService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Flux;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class TsGateway implements Gateway<Result, MartRequest> {

    private static final List<String> ALLOWED_DPS_IDS = Arrays.asList("E00011", "E00012", "E00013", "E00014", "E00015");


    @Override
    public Flux<Result> retrieve(MartRequest martRequest) {

        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), martRequest.getRequestId());
        if (CollectionUtils.isEmpty(martRequest.getIds()) || CollectionUtils.isEmpty(martRequest.getDps())) {
            return Flux.empty();
        }

        // Check if request contains any of the allowed DPS IDs
        boolean hasAllowedDps = martRequest.getDps().stream()
                .anyMatch(ALLOWED_DPS_IDS::contains);

        if (!hasAllowedDps) {
            log.info("Request {} does not contain any of the allowed DPS IDs: {}",
                    martRequest.getRequestId(), ALLOWED_DPS_IDS);
            return Flux.empty();
        }

        // Create dummy TsResult for testing
        return Flux.fromIterable(martRequest.getIds())
                .map(id -> createDummyTsResult(id, martRequest.getDps()));

    }

    private TsResult createDummyTsResult(String id, List<String> dataPoints) {
        Map<String, TsDataProtoBuf.TSDataDouble> values = new HashMap<>();

        // Filter dataPoints to only include allowed DPS IDs
        List<String> filteredDataPoints = dataPoints.stream()
                .filter(ALLOWED_DPS_IDS::contains)
                .toList();

        // Create dummy data for each allowed data point
        for (String dp : filteredDataPoints) {
            TsDataProtoBuf.TSDataDouble tsData = TsDataProtoBuf.TSDataDouble.newBuilder()
                    .setInvestmentId(id)
                    .setDpId(dp)
                    .addDates(1672531200000L) // 2023-01-01 timestamp
                    .addDates(1675209600000L) // 2023-02-01 timestamp
                    .addDates(1677628800000L) // 2023-03-01 timestamp
                    .addValues(100.0 + Math.random() * 50) // Random value between 100-150
                    .addValues(105.0 + Math.random() * 50) // Random value between 105-155
                    .addValues(110.0 + Math.random() * 50) // Random value between 110-160
                    .addCopyOverDateIndices(0) // Index pointing to first date
                    .addCopyOverDateIndices(1) // Index pointing to second date
                    .build();
            values.put(dp, tsData);
        }

        return new TsResult(id, values);
    }
}
