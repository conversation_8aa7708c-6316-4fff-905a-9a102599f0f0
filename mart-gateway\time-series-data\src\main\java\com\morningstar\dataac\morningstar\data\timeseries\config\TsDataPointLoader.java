package com.morningstar.dataac.morningstar.data.timeseries.config;

import com.morningstar.dataac.martgateway.core.common.service.datapoint.DatapointConfigFileService;
import com.morningstar.dataac.martgateway.core.datapointloader.config.DataPointLoaderInfo;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointRepository;
import com.morningstar.dataac.martgateway.core.datapointloader.entity.FixedIncomeDataPoint;
import com.morningstar.dataac.martgateway.core.datapointloader.repository.DocumentLoader;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.AbstractXmlDataPointLoader;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderContext;
import com.morningstar.dataac.martgateway.core.datapointloader.service.loader.DataPointLoaderDefinition;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.Element;

import java.util.HashMap;
import java.util.Map;

import static com.morningstar.dataac.martgateway.core.common.entity.RdbDataType.CURRENT_DATA;
import static com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.COLUMN;
import static com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.DATABASE_SCHEMA;
import static com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.DATAPOINT;
import static com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.DATAPOINT_GROUP;
import static com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.DATA_TYPE;
import static com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.GROUP;
import static com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.ID;
import static com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.IDLEVEL;
import static com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.NID;
import static com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.PARSE_FUNCTION;
import static com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.SRC;
import static com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.STORE_PROCEDURE;
import static com.morningstar.dataac.martgateway.core.datapointloader.entity.DataPointConstants.TABLES;

@DataPointLoaderDefinition(info = DataPointLoaderInfo.TIMESERIES)
@Slf4j
public class TsDataPointLoader extends AbstractXmlDataPointLoader {

    public TsDataPointLoader(DatapointConfigFileService datapointConfigFileService, DocumentLoader documentLoader) {
        super(datapointConfigFileService, documentLoader);
    }

    @Override
    public void processDocument(DataPointLoaderContext context, Document document) {
//        Map<String, Map<String, String>> fixedIncomeGroupColDpsMap = new HashMap<>();
//        for (Element dataPointGroupElement : document.getRootElement().elements(DATAPOINT_GROUP)) {
//            String groupName = dataPointGroupElement.attributeValue(GROUP);
//            String dataType = dataPointGroupElement.attributeValue(DATA_TYPE);
//            String idLevel = dataPointGroupElement.attributeValue(IDLEVEL);
//            String dataBaseSchema = dataPointGroupElement.attributeValue(DATABASE_SCHEMA);
//            String tableName = dataPointGroupElement.attributeValue(TABLES);
//            String storedProcedure = dataPointGroupElement.attributeValue(STORE_PROCEDURE);
//            String holdingsStoredProcedure = dataPointGroupElement.attributeValue("holdings-store-procedure");
//            String expireTime = dataPointGroupElement.attributeValue("expire-time");
//            String dateColumn = dataPointGroupElement.attributeValue("date-column");
//            fixedIncomeGroupColDpsMap.put(groupName, new HashMap<>());
//
//            for (Element dataPointElement : dataPointGroupElement.elements(DATAPOINT)) {
//                String id = dataPointElement.attributeValue(ID);
//                String nid = dataPointElement.attributeValue(NID);
//                String name = dataPointElement.attributeValue("name");
//                String src = dataPointElement.attributeValue(SRC);
//                String column = dataPointElement.attributeValue(COLUMN);
//                String parseFunction = dataPointElement.attributeValue(PARSE_FUNCTION);
//                String lookback = dataPointElement.attributeValue("lookback");
//
//                fixedIncomeGroupColDpsMap.get(groupName).put(column, nid);
//                DataPoint dataPoint = context.getOrCreateDataPoint(nid);
//                dataPoint.setId(id);
//                dataPoint.setSrc(src);
//                dataPoint.setGroupName(groupName);
//                dataPoint.setName(name);
//                dataPoint.setIdLevel(idLevel);
//
//                FixedIncomeDataPoint fixedIncomeDataPoint = FixedIncomeDataPoint.builder()
//                        .dataType(dataType)
//                        .column(column)
//                        .src(src)
//                        .dataBaseSchema(dataBaseSchema)
//                        .tables(tableName)
//                        .storeProcedure(storedProcedure)
//                        .holdingsStoreProcedure(holdingsStoredProcedure)
//                        .expireTime(expireTime)
//                        .parseFunction(parseFunction)
//                        .nid(nid)
//                        .id(id)
//                        .name(name)
//                        .idLevel(idLevel)
//                        .groupName(groupName)
//                        .lookback(lookback)
//                        .build();
//
//                if (CURRENT_DATA.getValue().equals(fixedIncomeDataPoint.getDataType())) {
//                    dataPoint.setCurrentFixedIncomeDataPoint(fixedIncomeDataPoint);
//                } else {
//                    fixedIncomeDataPoint.setDateColumn(dateColumn);
//                    dataPoint.setTsFixedIncomeDataPoint(fixedIncomeDataPoint);
//                }
//            }
//        }
//        DataPointRepository.addRdsGroupColDpsMap(fixedIncomeGroupColDpsMap);
    }

}
