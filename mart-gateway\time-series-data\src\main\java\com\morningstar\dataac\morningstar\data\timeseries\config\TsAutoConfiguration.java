package com.morningstar.dataac.morningstar.data.timeseries.config;

import com.morningstar.dataac.martgateway.core.common.repository.RedisReactiveRepo;
import com.morningstar.dataac.martgateway.core.datapointloader.config.DataPointAutoConfiguration;
import com.morningstar.dataac.martgateway.core.datapointloader.config.EnableDataPointLoaders;
import com.morningstar.dataac.morningstar.data.rds.config.RdsAutoConfiguration;
import com.morningstar.dataac.morningstar.data.rds.repository.RdsAsyncRepo;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.Semaphore;

@Configuration
@ConditionalOnProperty(name = "martgateway.modules.fixedincome.enable", havingValue = "true", matchIfMissing = false)
@EnableDataPointLoaders
@AutoConfigureAfter({DataPointAutoConfiguration.class, RdsAutoConfiguration.class})
@EnableConfigurationProperties({TsProperties.class})
public class TsAutoConfiguration {

//    @Bean("fixedIncomeCurrentCacheHelper")
//    @ConditionalOnMissingBean(FixedIncomeCurrentCacheHelper.class)
//    public FixedIncomeCurrentCacheHelper fixedIncomeCurrentCacheHelper(@Qualifier("appCacheRedisClient") RedisReactiveRepo appCacheRedisClient) {
//        return new FixedIncomeCurrentCacheHelper(appCacheRedisClient);
//    }
//
//    @Bean("fixedIncomeDataService")
//    @ConditionalOnMissingBean(FixedIncomeDataService.class)
//    public FixedIncomeDataService fixedIncomeDataService(@Qualifier("rdsAsyncRepo") RdsAsyncRepo rdsAsyncRepo, FixedIncomeCurrentCacheHelper fixedIncomeCurrentCacheHelper, @Qualifier("redisWriteSemaphore") Semaphore semaphore) {
//        return new FixedIncomeDataServiceImpl(rdsAsyncRepo, fixedIncomeCurrentCacheHelper, semaphore);
//    }
//
//    @Bean("fixedIncomeGateway")
//    @ConditionalOnMissingBean(FixedIncomeGateway.class)
//    public FixedIncomeGateway fixedIncomeGateway(FixedIncomeDataService fixedIncomeDataService) {
//        return new FixedIncomeGateway(fixedIncomeDataService);
//    }
}
