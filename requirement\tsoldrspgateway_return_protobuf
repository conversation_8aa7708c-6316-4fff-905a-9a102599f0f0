STAR-3447

update mart-api and mart-gateway code， implementation details:

1. mart-gateway
setp 1
TimeSeriesDatas that is from TsCacheData.proto file.
Make TsOldRspGatewayImpl.asyncRetrieveSecurities return Mono<TimeSeriesDatas>
Make TsOldRspGatewayImpl buildTsResponse(Flux<Result> results, Map<String, IdMapper> idMapperById) return Mono<TimeSeriesDatas>
  buildTsResponse(Flux<Result> results, Map<String, IdMapper> idMapperById)方法不依赖TSResponse

setp 2
make buildTsItemsFromResult(List<Result> results, Map<String, IdMapper> idMapperById) return TimeSeriesDatas directly
buildTsItemsFromTimeSeriesErrorResult, buildTsItemsFromResult 不依赖TSItem/TSData


2. marat-api

TimeSeriesController
   retrieveTimeSeriesData
      format the TimeSeriesDatas object to TSResponse
  retrieveTimeSeriesDataAsProtobuf
     no need to map(TSResponse::toProtobuf)


V2
mart-api TimeSeriesController依赖mart-gateway TsOldRspGatewayImpl
marat-api中的TimeSeriesController.retrieveTimeSeriesDataAsProtobuf 返回的对象是TsCacheDataForProtoBuf.TimeSeriesDatas，它通过TSResponse::toProtobuf转换。
为了提供代码的性能，减少中间的对象转换

修改的地方如下
1. mart-gateway

make buildTsItemsFromResult(List<Result> results, Map<String, IdMapper> idMapperById) return Pair<TimeSeriesDatas, Status>
make buildTsItemsFromResult(List<Result> results, Map<String, IdMapper> idMapperById) return Pair<TimeSeriesDatas, Status>
TSResponse 增加TimeSeriesDatas field, toProtobuf 方法直接返回该field，增加toJson()方法，把TimeSeriesDatas 返回TSResponse
update buildTsResponse()

2. marat-api

TimeSeriesController
   retrieveTimeSeriesData
      format the TimeSeriesDatas object to TSResponse
  retrieveTimeSeriesDataAsProtobuf
     no need to map(TSResponse::toProtobuf)