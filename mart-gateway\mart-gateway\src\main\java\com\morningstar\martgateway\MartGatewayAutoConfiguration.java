package com.morningstar.martgateway;

import com.amazonaws.services.s3.AmazonS3;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.morningstar.dataac.martgateway.core.common.entity.result.Result;
import com.morningstar.dataac.martgateway.core.common.entity.result.request.MartRequest;
import com.morningstar.dataac.martgateway.core.common.repository.RedisReactiveRepo;
import com.morningstar.dataac.martgateway.core.common.repository.RedisTsRepo;
import com.morningstar.dataac.martgateway.core.common.service.datapoint.DatapointConfigFileService;
import com.morningstar.dataac.martgateway.core.common.repository.S3Client;
import com.morningstar.dataac.martgateway.core.common.repository.SqsClient;
import com.morningstar.dataac.martgateway.data.rdb.repository.RdbDataAsyncRepo;
import com.morningstar.dataac.martgateway.data.rdb.repository.RdbDataRepo;
import com.morningstar.dataac.martgateway.service.Gateway;
import com.morningstar.dataac.morningstar.data.timeseries.service.gateway.TsGateway;
import com.morningstar.martgateway.applications.apiproxy.DirectApiGateway;
import com.morningstar.martgateway.applications.calculation.CalcServiceGateway;
import com.morningstar.martgateway.applications.calculation.CustomCalculationServiceGateway;
import com.morningstar.martgateway.applications.current.CurrentGateway;
import com.morningstar.martgateway.applications.dataservice.DSGateway;
import com.morningstar.martgateway.applications.delta.DeltaGateway;
import com.morningstar.martgateway.applications.economic.EconomicDataGateway;
import com.morningstar.martgateway.applications.fixincome.FIGateway;
import com.morningstar.martgateway.applications.lakehouse.LakeHouseGateway;
import com.morningstar.martgateway.applications.rdb.RdbGateway;
import com.morningstar.martgateway.applications.timeseries.TSGateway;
import com.morningstar.martgateway.applications.tscacheproxy.TsCacheProxyApplication;
import com.morningstar.martgateway.domains.core.pipeline.MartRequestDataAssemblerService;
import com.morningstar.dataac.martgateway.core.datapointloader.config.EnableDataPointLoaders;
import com.morningstar.martgateway.domains.calc.CustomCalculationAPICaller;
import com.morningstar.martgateway.domains.calc.CustomCalculationService;
import com.morningstar.martgateway.domains.delta.DeltaDetectionService;
import com.morningstar.martgateway.domains.delta.DeltaValueService;
import com.morningstar.dataac.martgateway.core.entitlement.service.EntitlementCacheService;
import com.morningstar.dataac.martgateway.core.entitlement.service.EntitlementRequestFilterService;
import com.morningstar.dataac.martgateway.core.entitlement.service.requestwrapper.EntitlementInvestmentApiRequestWrapper;
import com.morningstar.dataac.martgateway.core.entitlement.service.requestwrapper.EntitlementMartRequestWrapper;
import com.morningstar.martgateway.domains.economic.repository.EconomicRepo;
import com.morningstar.martgateway.domains.rdb.helper.ExchangeRateCache;
import com.morningstar.martgateway.interfaces.EconomicDataGatewayImpl;
import com.morningstar.martgateway.domains.fi.dao.FIDao;
import com.morningstar.martgateway.domains.fi.service.FICaller;
import com.morningstar.martgateway.domains.fi.service.FIService;
import com.morningstar.martgateway.domains.index.repository.IndexRepo;
import com.morningstar.martgateway.domains.index.service.IndexDataService;
import com.morningstar.martgateway.domains.index.service.IndexDataServiceImpl;
import com.morningstar.martgateway.domains.language.LanguageService;
import com.morningstar.martgateway.domains.apiproxy.entity.SecuritiesRequestEntity;
import com.morningstar.martgateway.domains.apiproxy.entity.SecuritiesResponseEntity;
import com.morningstar.martgateway.domains.apiproxy.service.DirectApiProxyService;
import com.morningstar.martgateway.domains.apiproxy.service.DynamicDataService;
import com.morningstar.martgateway.domains.apiproxy.service.RegularDataService;
import com.morningstar.martgateway.domains.calc.CurrentDataRetrievalService;
import com.morningstar.martgateway.domains.calc.CalcAPICaller;
import com.morningstar.martgateway.domains.calc.CalculationService;
import com.morningstar.martgateway.domains.calclib.CalculationLibConversionService;
import com.morningstar.martgateway.domains.calclib.CalculationLibService;
import com.morningstar.martgateway.domains.calclib.conversion.ConversionCenter;
import com.morningstar.martgateway.domains.calclib.conversion.CurrencyConversion;
import com.morningstar.martgateway.domains.calclib.conversion.CurrencyConversionHelper;
import com.morningstar.martgateway.domains.calclib.conversion.ExchangeRateLoader;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.martgateway.domains.core.pipeline.MartDataPipeline;
import com.morningstar.martgateway.domains.current.CategoryIdLevelDataService;
import com.morningstar.martgateway.domains.current.CurrentManager;
import com.morningstar.martgateway.domains.current.CurrentService;
import com.morningstar.martgateway.domains.dataservice.customcalc.CustomCalcProcessor;
import com.morningstar.martgateway.domains.dataservice.dao.DataResultDao;
import com.morningstar.martgateway.domains.dataservice.service.DataServiceCaller;
import com.morningstar.martgateway.domains.dataservice.service.DataServiceTsCaller;
import com.morningstar.dataac.martgateway.core.entitlement.service.DataEntitlementService;
import com.morningstar.martgateway.domains.privatemodel.PrivateModelPortfolioService;
import com.morningstar.martgateway.domains.rdb.cache.DynamicTsDataLoader;
import com.morningstar.martgateway.domains.rdb.cache.TsDataLoader;
import com.morningstar.martgateway.domains.rdb.cache.TsFrequencyDataLoader;
import com.morningstar.martgateway.domains.rdb.helper.RdbCurrentCacheDataHelper;
import com.morningstar.martgateway.domains.rdb.helper.RdbTsCacheDataHelper;
import com.morningstar.martgateway.domains.rdb.service.DynamicRdbService;
import com.morningstar.martgateway.domains.rdb.service.DynamicRdbTsService;
import com.morningstar.martgateway.domains.rdb.service.RdbCurrentService;
import com.morningstar.martgateway.domains.rdb.service.RdbTsService;
import com.morningstar.martgateway.domains.timeseries.entity.TSResponse;
import com.morningstar.martgateway.domains.timeseries.service.CloudHistoricalService;
import com.morningstar.martgateway.domains.timeseries.service.TScacheService;
import com.morningstar.martgateway.domains.tscacheproxy.service.TsCacheProxyService;
import com.morningstar.dataac.martgateway.core.entitlement.config.filterloader.EnableEntitlementFilterLoaders;
import com.morningstar.martgateway.infrastructures.config.ServiceLoggingAspect;
import com.morningstar.martgateway.infrastructures.http.WebClientFactory;
import com.morningstar.martgateway.infrastructures.repo.data.S3DataRepo;
import com.morningstar.dataac.martgateway.core.datapointloader.repository.DocumentLoader;
import com.morningstar.martgateway.infrastructures.repo.investmentapiproxy.DynamicDataRepository;
import com.morningstar.martgateway.infrastructures.repo.investmentapiproxy.ProxyUriBuilder;
import com.morningstar.martgateway.infrastructures.repo.investmentapiproxy.RegularDataRepository;
import com.morningstar.dataac.martgateway.core.uim.service.UimTokenService;
import com.morningstar.martgateway.interfaces.ClearCacheGatewayImpl;
import com.morningstar.martgateway.interfaces.DeltaGatewayImpl;
import com.morningstar.martgateway.interfaces.DoApiProxyImpl;
import com.morningstar.martgateway.interfaces.GridViewGatewayImpl;
import com.morningstar.martgateway.interfaces.HoldingDataGatewayImpl;
import com.morningstar.martgateway.interfaces.MartApiGatewayImpl;
import com.morningstar.dataac.martgateway.service.MartGateway;
import com.morningstar.martgateway.interfaces.TsNewRspGatewayImpl;
import com.morningstar.martgateway.interfaces.TsOldRspGatewayImpl;
import com.morningstar.martgateway.interfaces.model.FormatConverter;
import com.morningstar.dataac.martgateway.core.common.entity.investmentapi.InvestmentApiRequest;
import com.morningstar.martgateway.util.ExcludeFromTests;
import com.morningstar.dataac.martgateway.core.common.util.IdMapUtil;
import com.morningstar.martgateway.util.RequireIdUtil;
import com.morningstar.martgateway.util.apiproxy.DataPointSourceUtil;
import com.morningstar.martgateway.util.apiproxy.InvestmentHandledDataPointUtil;
import com.morningstar.martgateway.util.apiproxy.WhiteListCache;
import com.morningstar.redshift.jdbc.RedshiftRepo;

import java.util.List;

import java.util.concurrent.Semaphore;
import org.dom4j.io.SAXReader;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Primary;
import org.springframework.data.elasticsearch.client.reactive.ReactiveElasticsearchClient;
import org.springframework.data.elasticsearch.client.reactive.ReactiveRestClients;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.EnableAsync;

@Lazy
@Configuration
@EnableConfigurationProperties({MartGatewayProperties.class})
@ExcludeFromTests
@EnableAsync
@EnableDataPointLoaders(basePackages = {"com.morningstar.martgateway.infrastructures.repo.datapoint"})
@EnableEntitlementFilterLoaders(basePackages = {"com.morningstar.martgateway.infrastructures.repo.entitlement"})
public class MartGatewayAutoConfiguration implements InitializingBean {

    private final MartGatewayProperties martGatewayProperties;

    @Autowired
    public MartGatewayAutoConfiguration(MartGatewayProperties martGatewayProperties) {
        this.martGatewayProperties = martGatewayProperties;
    }

    @Bean(name = "calcAPICaller")
    @ConditionalOnMissingBean(name = "calcAPICaller")
    public CalcAPICaller calcAPICaller() {
        return new CalcAPICaller(martGatewayProperties.getCalculation().getBaseUrl(),
                martGatewayProperties.getCalculation().getUri(),
                martGatewayProperties.getCalculation().getUserAgent(),
                martGatewayProperties.getCalculation().getTimeout());
    }

    @Bean(name = "customCalculationAPICaller")
    @ConditionalOnMissingBean(name = "customCalculationAPICaller")
    public CustomCalculationAPICaller customCalculationAPICaller(UimTokenService uimTokenService) {
        return new CustomCalculationAPICaller(martGatewayProperties.getCalculation().getCustomCalculationUrl(),
                martGatewayProperties.getCalculation().getCustomCalculationEndpoint(),
                martGatewayProperties.getCalculation().getCustomCalculationTimeout(),
                uimTokenService);
    }

    @Bean(name = "customCalculationService")
    @ConditionalOnMissingBean(name = "customCalculationService")
    public CustomCalculationService customCalculationService(CurrentDataRetrievalService currentDataRetrievalService, CustomCalculationAPICaller customCalculationAPICaller) {
        return new CustomCalculationService(currentDataRetrievalService, customCalculationAPICaller);
    }

    @Bean(name = "bmkRfRetrievalService")
    @ConditionalOnMissingBean(name = "bmkRfRetrievalService")
    public CurrentDataRetrievalService bmkRfRetrievalService(CurrentManager currentManager, RdbCurrentService rdbCurrentService) {
        return new CurrentDataRetrievalService(currentManager, rdbCurrentService);
    }

    @Bean(name = "calculationService")
    @ConditionalOnMissingBean(name = "calculationService")
    public CalculationService calculationService(CurrentDataRetrievalService currentDataRetrievalService, CalcAPICaller calcAPICaller) {
        return new CalculationService(currentDataRetrievalService, calcAPICaller);
    }

    @Bean(name = "exchangeRateLoader")
    @ConditionalOnMissingBean(name = "exchangeRateLoader")
    public ExchangeRateLoader exchangeRateLoader(@Qualifier("syncStorageDataTemplate") RedisTemplate<String, String> redisTemplate) {
        return new ExchangeRateLoader(redisTemplate);
    }

    @Bean(name = "conversionCenter")
    @ConditionalOnMissingBean(name = "conversionCenter")
    public ConversionCenter conversionCenter(ExchangeRateLoader exchangeRateLoader) {
        return new ConversionCenter(exchangeRateLoader);
    }

    @Bean(name = "currencyConversion")
    @ConditionalOnMissingBean(name = "currencyConversion")
    public CurrencyConversion currencyConversion(ConversionCenter conversionCenter) {
        return new CurrencyConversion(conversionCenter);
    }

    @Bean(name = "conversionHelper")
    @ConditionalOnMissingBean(name = "conversionHelper")
    public CurrencyConversionHelper conversionHelper(CurrencyConversion currencyConversion) {
        return new CurrencyConversionHelper(currencyConversion);
    }

    @Bean(name = "calculationLibConversionService")
    @ConditionalOnMissingBean(name = "calculationLibConversionService")
    public CalculationLibConversionService calculationLibConversionService(CurrencyConversionHelper currencyConversionHelper) {
        return new CalculationLibConversionService(currencyConversionHelper);
    }

    @Bean(name = "calculationLibService")
    @ConditionalOnMissingBean(name = "calculationLibService")
    public CalculationLibService calculationLibService(CurrentManager currentManager, TScacheService tscacheService, CalculationLibConversionService calculationLibConversionService, RdbGateway rdbGateway, RdbTsService rdbTsService) {
        return new CalculationLibService(currentManager, tscacheService, calculationLibConversionService, rdbGateway, rdbTsService);
    }

    @Bean(name = "currentManager")
    @ConditionalOnMissingBean(name = "currentManager")
    public CurrentManager currentManager(CurrentService currentService,
            CategoryIdLevelDataService categoryIdLevelDataService) {
        return new CurrentManager(currentService, categoryIdLevelDataService);
    }


    @Bean(name = "currentService")
    @ConditionalOnMissingBean(name = "currentService")
    public CurrentService currentService(S3DataRepo s3DataRepo,
            @Qualifier("syncStorageDataTemplate") RedisTemplate<String, String> syncDataStorageTemplate,
            @Qualifier("syncCacheDataTemplate") RedisTemplate<String, String> syncDataCacheTemplate) {
        return new CurrentService(s3DataRepo, syncDataStorageTemplate, syncDataCacheTemplate);
    }

    @Bean(name = "categoryIdLevelDataService")
    @ConditionalOnMissingBean(name = "categoryIdLevelDataService")
    public CategoryIdLevelDataService categoryIdLevelDataService(S3DataRepo s3DataRepo,
            @Qualifier("syncStorageDataTemplate") RedisTemplate<String, String> syncDataStorageTemplate,
            @Qualifier("syncCacheDataTemplate") RedisTemplate<String, String> syncDataCacheTemplate) {
        return new CategoryIdLevelDataService(s3DataRepo, syncDataStorageTemplate, syncDataCacheTemplate);
    }

    @Bean(name = "s3GetService")
    @ConditionalOnMissingBean(name = "s3GetService")
    public S3DataRepo s3GetService(@Qualifier("s3Helper") S3Client s3Client) {
        return new S3DataRepo(s3Client);
    }

    @Bean(name = "dataResultDao")
    @ConditionalOnMissingBean(name = "dataResultDao")
    public DataResultDao dataResultDao(@Qualifier("s3Helper") S3Client s3Client,
            @Value(value = "${spring.profiles}") String environment,
            @Qualifier("syncStorageDataTemplate") RedisTemplate<String, String> syncStorageDataTemplate) {
        return new DataResultDao(s3Client, environment, martGatewayProperties.getAws().getS3().getPrefix(), martGatewayProperties.getAws().getS3().getBucket(), syncStorageDataTemplate);
    }

    @Bean(name = "dataServiceCaller")
    @ConditionalOnMissingBean(name = "dataServiceCaller")
    public DataServiceCaller dataServiceCaller(CurrentService currentService,
            @Qualifier("syncCacheDataTemplate") RedisTemplate<String, String> syncCacheDataTemplate,
            DataResultDao dataResultDao, CustomCalcProcessor customCalcProcessor) {
        return new DataServiceCaller(currentService, syncCacheDataTemplate, dataResultDao, customCalcProcessor);
    }

    @Bean(name = "dataServiceTsCaller")
    @ConditionalOnMissingBean(name = "dataServiceTsCaller")
    public DataServiceTsCaller dataServiceTsCaller(@Qualifier("syncCacheDataTemplate") RedisTemplate<String, String> syncCacheDataTemplate,
            DataResultDao dataResultDao,
            CustomCalcProcessor customCalcProcessor) {
        return new DataServiceTsCaller(syncCacheDataTemplate, dataResultDao, customCalcProcessor);
    }

    @Bean(name = "fixedIncomeDao")
    @ConditionalOnMissingBean(name = "fixedIncomeDao")
    public FIDao fixedIncomeDao(AmazonS3 s3Client,
            @Value(value = "${spring.profiles}") String environment,
            @Qualifier("syncStorageDataTemplate") RedisTemplate<String, String> syncStorageDataTemplate,
            RdbCurrentService rdbCurrentService) {
        return new FIDao(s3Client, environment, syncStorageDataTemplate, martGatewayProperties.getAws().getS3().getPrefix(), rdbCurrentService);
    }

    @Bean(name = "fixedIncomeService")
    @ConditionalOnMissingBean(name = "fixedIncomeService")
    public FIService fixedIncomeService(FIDao fiDao) {
        return new FIService(fiDao);
    }

    @Bean(name = "fixedIncomeCaller")
    @ConditionalOnMissingBean(name = "fixedIncomeCaller")
    public FICaller fixedIncomeCaller(FIService fixedIncomeService) {
        return new FICaller(fixedIncomeService);
    }

    @Bean(name = "calcServiceGateway")
    @ConditionalOnMissingBean(name = "calcServiceGateway")
    public CalcServiceGateway calcServiceGateway(CalculationService calculationService,
                                        CalculationLibService calculationLibService) {
        return new CalcServiceGateway(calculationService, calculationLibService);
    }

    @Bean(name = "customCalculationServiceGateway")
    @ConditionalOnMissingBean(name = "customCalculationServiceGateway")
    public CustomCalculationServiceGateway customCalculationServiceGateway(CustomCalculationService customCalculationService,
            CalculationLibService calculationLibService) {
        return new CustomCalculationServiceGateway(customCalculationService, calculationLibService);
    }


    @Bean(name = "currentGateway")
    @ConditionalOnMissingBean(name = "currentGateway")
    public CurrentGateway currentGateway(CurrentManager manager) {
        return new CurrentGateway(manager);
    }

    @Bean(name = "dsGateWay")
    @ConditionalOnMissingBean(name = "dsGateWay")
    public DSGateway dsGateWay(DataServiceCaller dataServiceCaller, DataServiceTsCaller dataServiceTsCaller) {
        return new DSGateway(dataServiceCaller, dataServiceTsCaller);
    }

    @Bean(name = "fiGateWay")
    @ConditionalOnMissingBean(name = "fiGateWay")
    public FIGateway fiGateWay(FICaller fiCaller) {
        return new FIGateway(fiCaller);
    }

    @Bean(name = "tsGateway")
    @ConditionalOnMissingBean(name = "tsGateway")
    public TSGateway tsGateway(TScacheService tsCacheService) {
        return new TSGateway(tsCacheService);
    }

    @Bean(name = "privateModelPortfolioService")
    @ConditionalOnMissingBean(value = PrivateModelPortfolioService.class)
    public PrivateModelPortfolioService privateModelPortfolioService(RedshiftRepo redshiftRepo,
            @Value(value = "${spring.profiles}") String environment, CustomCalcProcessor customCalcProcessor) {
        return new PrivateModelPortfolioService(redshiftRepo, environment, customCalcProcessor);
    }

    @Bean(name = "lakeHouseGateway")
    @ConditionalOnMissingBean(value = LakeHouseGateway.class)
    @ConditionalOnProperty(prefix = "martgateway", name = "mart-api", havingValue = "false")
    public LakeHouseGateway lakeHouseGateway(PrivateModelPortfolioService privateModelPortfolioService) {
        return new LakeHouseGateway(privateModelPortfolioService);
    }

    @Bean(name = "cloudHistoricalService")
    @ConditionalOnMissingBean(name = "cloudHistoricalService")
    public CloudHistoricalService cloudHistoricalService(S3DataRepo s3DataRepo,
            CurrentService currentService,
            @Qualifier("syncCacheDataTemplate") RedisTemplate<String, String> syncDataCacheTemplate) {
        return new CloudHistoricalService(s3DataRepo, currentService, syncDataCacheTemplate);
    }

    @Bean(name = "tsOldRspGateway")
    @ConditionalOnMissingBean(name = "tsOldRspGateway")
    public MartGateway<TSResponse, MartRequest> tsOldRspGateway(MartDataPipeline martDataPipeline, TsCacheProxyApplication tsCacheProxyApplication, IdMapUtil idMapUtil,
            DataEntitlementService<MartRequest> dataEntitlementService, EntitlementRequestFilterService<MartRequest, EntitlementMartRequestWrapper> entitlementMartRequestFilterService, ExchangeRateCache exchangeRateCache) {
        return new TsOldRspGatewayImpl(martDataPipeline, tsCacheProxyApplication, idMapUtil, dataEntitlementService, entitlementMartRequestFilterService, exchangeRateCache);
    }

    @Bean(name = "tsNewRspGateway")
    @ConditionalOnMissingBean(name = "tsNewRspGateway")
    public MartGateway<InvestmentResponse, MartRequest> tsNewRspGateway(MartDataPipeline martDataPipeline, TsCacheProxyApplication tsCacheProxyApplication, IdMapUtil idMapUtil,
            DataEntitlementService<MartRequest> dataEntitlementService, EntitlementRequestFilterService<MartRequest, EntitlementMartRequestWrapper> entitlementMartRequestFilterService) {
        return new TsNewRspGatewayImpl(martDataPipeline, tsCacheProxyApplication, idMapUtil, dataEntitlementService, entitlementMartRequestFilterService);
    }

    @Bean(name = "tsCacheProxyApplication")
    @ConditionalOnMissingBean(name = "tsCacheProxyApplication")
    public TsCacheProxyApplication tsCacheProxyApplication(TsCacheProxyService tsCacheProxyService) {
        return new TsCacheProxyApplication(tsCacheProxyService);
    }

    @Bean(name = "apiProxyGateway")
    @ConditionalOnMissingBean(name = "apiProxyGateway")
    public DirectApiGateway apiProxyGateway(DirectApiProxyService directApiProxyService) {
        return new DirectApiGateway(directApiProxyService);
    }

    @Bean(name = "doApiProxyImpl")
    @ConditionalOnMissingBean(name = "doApiProxyImpl")
    public MartGateway<SecuritiesResponseEntity, SecuritiesRequestEntity> doApiProxyImpl(DirectApiGateway directApiGateway) {
        return new DoApiProxyImpl(directApiGateway);
    }

    @Bean(name = "tScacheCaller")
    @ConditionalOnMissingBean(name = "tScacheCaller")
    public TScacheService tScacheCaller() {
        return new TScacheService(martGatewayProperties.getTscache().getServer());
    }

    @Bean(name = "tsCacheProxyService")
    @ConditionalOnMissingBean(name = "tsCacheProxyService")
    public TsCacheProxyService tsCacheProxyService() {
        return new TsCacheProxyService(martGatewayProperties.getTscache().getServer());
    }

    @Bean(name = "s3Helper")
    @ConditionalOnMissingBean(name = "s3Helper")
    public S3Client s3ClientGlobal(@Qualifier("s3Client") AmazonS3 amazonS3) {
        return new S3Client(martGatewayProperties.getAws().getS3().getBucket(), amazonS3);
    }

    @Bean(name = "documentLoader")
    @ConditionalOnMissingBean(name = "documentLoader")
    public DocumentLoader documentLoader(@Autowired DatapointConfigFileService datapointConfigFileService) {
        return new DocumentLoader(new SAXReader(), datapointConfigFileService);
    }

    @Bean(name = "customCalcProcessor")
    @ConditionalOnMissingBean(name = "customCalcProcessor")
    public CustomCalcProcessor customCalcProcessor() {
        return new CustomCalcProcessor();
    }

    @Bean(name = "redshiftRepo", destroyMethod = "closeRedShiftConnection")
    @ConditionalOnMissingBean(value = RedshiftRepo.class)
    public RedshiftRepo redshiftRepo() {
        return new RedshiftRepo(martGatewayProperties.getRedshift().getDbUrl(), martGatewayProperties.getRedshift().getUser(), martGatewayProperties.getRedshift().getPassword());
    }

    @Bean(name = "rdbCacheDataHelper")
    public RdbCurrentCacheDataHelper rdbCacheDataHelper(@Qualifier("appCacheRedisClient") RedisReactiveRepo appCacheRedisClient, @Qualifier("redisWriteSemaphore") Semaphore semaphore) {
        return new RdbCurrentCacheDataHelper(appCacheRedisClient, semaphore);
    }

    @Bean(name = "rdbTsCacheDataHelper")
    public RdbTsCacheDataHelper rdbTsCacheDataHelper(RedisTsRepo redisTsRepo, @Qualifier("redisWriteSemaphore") Semaphore semaphore) throws JsonMappingException {
        return new RdbTsCacheDataHelper(redisTsRepo, semaphore);
    }

    @Bean(name = "rdbService")
    @DependsOn({"rdbCacheDataHelper"})
    @Primary
    public RdbCurrentService rdbService(RdbDataAsyncRepo rdbDataAsyncRepo,
            RdbCurrentCacheDataHelper rdbCurrentCacheDataHelper) {
        return new RdbCurrentService(rdbDataAsyncRepo, rdbCurrentCacheDataHelper, martGatewayProperties.isMartApi());
    }

    @Bean(name = "languageService")
    public LanguageService languageService(RdbDataAsyncRepo rdbDataAsyncRepo,
            RdbCurrentCacheDataHelper rdbCurrentCacheDataHelper) {
        return new LanguageService(rdbDataAsyncRepo, rdbCurrentCacheDataHelper, martGatewayProperties.isMartApi());
    }

    @Bean(name = "parameterService")
    public DynamicRdbService parameterService(RdbDataAsyncRepo rdbDataAsyncRepo,
            RdbCurrentCacheDataHelper rdbCurrentCacheDataHelper) {
        return new DynamicRdbService(rdbDataAsyncRepo, rdbCurrentCacheDataHelper, martGatewayProperties.isMartApi());
    }

    @Bean(name = "webClientFactory")
    @ConditionalOnMissingBean(name = "webClientFactory")
    public WebClientFactory webClientFactory() {
        return new WebClientFactory();
    }

    @Bean(name = "proxyUriBuilder")
    @ConditionalOnMissingBean(name = "proxyUriBuilder")
    public ProxyUriBuilder proxyUriBuilder() {
        return new ProxyUriBuilder(martGatewayProperties.getDirect().getPid());
    }


    @Bean(name = "regularDataService")
    @ConditionalOnMissingBean(name = "regularDataService")
    public RegularDataService regularDataService(RegularDataRepository regularDataRepository) {
        return new RegularDataService(regularDataRepository);
    }

    @Bean(name = "regularDataRepository")
    @ConditionalOnMissingBean(name = "regularDataRepository")
    public RegularDataRepository regularDataRepository(WebClientFactory webClientFactory, ProxyUriBuilder proxyUriBuilder) {
        return new RegularDataRepository(
                webClientFactory.create(martGatewayProperties.getDirect().getStandardUrl(), martGatewayProperties.getDirect().getRegularClientTimeout()),
                proxyUriBuilder,
                martGatewayProperties.getDirect().getRegularBackOffSeconds(),
                martGatewayProperties.getDirect().getRegularRetries()
        );
    }

    @Bean(name = "dynamicDataService")
    @ConditionalOnMissingBean(name = "dynamicDataService")
    public DynamicDataService dynamicDataService(DynamicDataRepository dynamicDataRepository) {
        return new DynamicDataService(dynamicDataRepository, martGatewayProperties.getDirect().getInvestmentsPerCall());
    }

    @Bean(name = "dynamicDataRepository")
    @ConditionalOnMissingBean(name = "dynamicDataRepository")
    public DynamicDataRepository dynamicDataRepository(WebClientFactory webClientFactory, ProxyUriBuilder proxyUriBuilder) {
        return new DynamicDataRepository(
                webClientFactory.create(
                        martGatewayProperties.getDirect().getStandardUrl(),
                        martGatewayProperties.getDirect().getDynamicClientTimeout()),
                proxyUriBuilder,
                martGatewayProperties.getDirect().getDatapointsPerCall(),
                martGatewayProperties.getDirect().getDynamicBackOffSeconds(),
                martGatewayProperties.getDirect().getDynamicRetries()
        );
    }

    @Bean(name = "requireIdUtil")
    @ConditionalOnMissingBean(name = "requireIdUtil")
    public RequireIdUtil requireIdUtil(RdbGateway rdbGateway) {
        return new RequireIdUtil(rdbGateway);
    }

    @Bean(name = "directService")
    @ConditionalOnMissingBean(name = "directService")
    public DirectApiProxyService directService(
            RegularDataService regularDataService,
            DynamicDataService dynamicDataService
    ) {
        return new DirectApiProxyService(regularDataService, dynamicDataService);
    }

    @Bean
    @ConditionalOnMissingBean(name = "tsFrequencyDataLoader")
    public TsFrequencyDataLoader tsFrequencyDataLoader(RdbDataAsyncRepo rdbDataAsyncRepo) {
        return new TsFrequencyDataLoader(rdbDataAsyncRepo);
    }

    @Bean
    @ConditionalOnMissingBean(name = "tsDataLoader")
    public TsDataLoader tsDataLoader(RdbDataAsyncRepo rdbDataAsyncRepo) {
        return new TsDataLoader(rdbDataAsyncRepo);
    }

    @Bean
    @ConditionalOnMissingBean(name = "dynamicTsDataLoader")
    public DynamicTsDataLoader dynamicTsDataLoader(RdbDataAsyncRepo rdbDataAsyncRepo) {
        return new DynamicTsDataLoader(rdbDataAsyncRepo);
    }

    @Bean
    @ConditionalOnMissingBean(name = "rdbTsService")
    public RdbTsService rdbTsService(RdbTsCacheDataHelper rdbCacheDataHelper, TsFrequencyDataLoader tsFrequencyDataLoader,
            TsDataLoader tsDataLoader, ApplicationEventPublisher applicationEventPublisher, @Value("${martgateway.martapi:false}") boolean isMartApi) {
        return new RdbTsService(rdbCacheDataHelper, tsFrequencyDataLoader, tsDataLoader, applicationEventPublisher, isMartApi);
    }

    @Bean
    @ConditionalOnMissingBean(name = "dynamicRdbTsService")
    public DynamicRdbTsService dynamicRdbTsService(RdbTsCacheDataHelper rdbCacheDataHelper, TsFrequencyDataLoader tsFrequencyDataLoader,
                                                   DynamicTsDataLoader tsDataLoader, ApplicationEventPublisher applicationEventPublisher, @Value("${martgateway.martapi:false}") boolean isMartApi) {
        return new DynamicRdbTsService(rdbCacheDataHelper, tsFrequencyDataLoader, tsDataLoader, applicationEventPublisher, isMartApi);
    }

    @Bean
    public MartRequestDataAssemblerService martRequestDataAssemblerService(List<Gateway<Result, MartRequest>> gateways){
        return new MartRequestDataAssemblerService(gateways);
    }

    @Bean
    @ConditionalOnMissingBean(name = "martDataPipeline")
    public MartDataPipeline martDataPipeline(MartRequestDataAssemblerService martRequestDataAssemblerService,
                                             RequireIdUtil requireIdUtil,
                                             IdMapUtil idMapUtil,
                                             @Qualifier("entitlementCacheService") EntitlementCacheService entitlementCacheService) {
        return new MartDataPipeline(martRequestDataAssemblerService, requireIdUtil, idMapUtil, entitlementCacheService);
    }

    @Bean(name = "gridViewGateway")
    @ConditionalOnMissingBean(name = "gridViewGateway")
    public GridViewGatewayImpl gridViewGateway(
            MartDataPipeline martDataPipeline,
            DataEntitlementService dataEntitlementService,
            EntitlementRequestFilterService<InvestmentApiRequest, EntitlementInvestmentApiRequestWrapper> entitlementInvestmentApiRequestFilterService,
            DirectApiGateway directApiGateway,
            DeltaGateway deltaGateway,
            DataPointSourceUtil dataPointSourceUtil,
            IdMapUtil idMapUtil,
            FormatConverter formatConverter, @Value("${martgateway.calculation.useNewCCS:false}") Boolean useNewCCS) {
        return new GridViewGatewayImpl(martDataPipeline, dataEntitlementService,
                entitlementInvestmentApiRequestFilterService, directApiGateway, deltaGateway, dataPointSourceUtil, idMapUtil,
                formatConverter, useNewCCS);
    }

    @Bean(name = "indexDataService")
    @ConditionalOnMissingBean(name = "indexDataService")
    public IndexDataService indexDataService(
      IndexRepo indexRepo) {
        return new IndexDataServiceImpl(indexRepo);
    }

    @Bean(name = "indexAsyncRepo")
    @ConditionalOnMissingBean(name = "indexAsyncRepo")
    public IndexRepo indexAsyncRepo(
            RdbDataRepo rdbDataRepo) {
        return new IndexRepo(rdbDataRepo);
    }

    @Bean(name = "economicRepo")
    @ConditionalOnMissingBean(name = "economicRepo")
    public EconomicRepo economicRepo(
            RdbDataRepo rdbDataRepo) {
        return new EconomicRepo(rdbDataRepo);
    }

    @Bean(name = "martGateway")
    @ConditionalOnMissingBean(name = "martGateway")
    public MartApiGatewayImpl martApiGateway(MartDataPipeline martDataPipeline,
            CurrentGateway currentGateway, @Value("${martgateway.calculation.useNewCCS:false}") Boolean useNewCCS) {
        return new MartApiGatewayImpl(martDataPipeline, currentGateway, useNewCCS);
    }

    @Bean(name = "whiteListCache")
    @ConditionalOnMissingBean(name = "whiteListCache")
    public WhiteListCache whiteListCache(@Qualifier("datapointConfigFileService") DatapointConfigFileService datapointConfigFileService) throws Exception {
        return new WhiteListCache(martGatewayProperties.getAws().getS3().getDataPointWhiteListConfig(), datapointConfigFileService);
    }

    @Bean(name = "dataPointSourceUtil")
    @ConditionalOnMissingBean(name = "dataPointSourceUtil")
    public DataPointSourceUtil dataPointSourceUtil(WhiteListCache whiteListCache) {
        return new DataPointSourceUtil(new InvestmentHandledDataPointUtil(), whiteListCache);
    }

    @Bean(name = "holdingDataGateway")
    @ConditionalOnMissingBean(name = "holdingDataGateway")
    public HoldingDataGatewayImpl investmentGateway(
            MartDataPipeline martDataPipeline) {
        return new HoldingDataGatewayImpl(martDataPipeline);
    }

    @Bean(name = "clearCacheGateway")
    @ConditionalOnMissingBean(name = "clearCacheGateway")
    public ClearCacheGatewayImpl clearCacheGateway(SqsClient sqsClient, @Value(value = "${martgateway.aws.sqs.accountId}") String accountId, @Value(value = "${martgateway.aws.sqs.rdb}") String sqsName) {
        return new ClearCacheGatewayImpl(sqsClient, accountId, sqsName);
    }

    @Bean(name = "formatConverter")
    @ConditionalOnMissingBean(FormatConverter.class)
    public FormatConverter formatConverter() {
        return new FormatConverter();
    }

    @Bean(name = "economicDataGatewayImpl")
    @ConditionalOnMissingBean(name = "economicDataGatewayImpl")
    public EconomicDataGatewayImpl economicDataService(
            EconomicDataGateway economicDataGateway) {
        return new EconomicDataGatewayImpl(economicDataGateway);
    }

    @Bean(name = "economicDataGateway")
    @ConditionalOnMissingBean(EconomicDataGateway.class)
    public EconomicDataGateway economicDataGateway(EconomicRepo economicRepo) {
        return new EconomicDataGateway(economicRepo);
    }

    @Bean(name = "deltaGatewayImpl")
    @ConditionalOnMissingBean(name = "deltaGatewayImpl")
    public DeltaGatewayImpl deltaGateway(
            DeltaGateway deltaGateway,
            IdMapUtil idMapUtil,
            FormatConverter formatConverter
    ) {
        return new DeltaGatewayImpl(deltaGateway, idMapUtil, formatConverter);
    }

    @Bean(name = "deltaGateway")
    @ConditionalOnMissingBean(DeltaGateway.class)
    public DeltaGateway deltaGateway(DeltaDetectionService deltaDetectionService, DeltaValueService deltaValueService) {
        return new DeltaGateway(deltaDetectionService, deltaValueService);
    }

    @Bean(name = "esChangeLogClient")
    @ConditionalOnMissingBean(name = "esChangeLogClient")
    public ReactiveElasticsearchClient reactiveElasticsearchClient(@Value("${martgateway.delta.esChangeLogUrl}") String esChangeLogUrl) {
        org.springframework.data.elasticsearch.client.ClientConfiguration clientConfiguration = org.springframework.data.elasticsearch.client.ClientConfiguration.builder()
                .connectedTo(esChangeLogUrl)
                .build();
        return ReactiveRestClients.create(clientConfiguration);
    }

    @Bean(name = "deltaDetectionService")
    @ConditionalOnMissingBean(DeltaDetectionService.class)
    public DeltaDetectionService deltaService(@Qualifier("esChangeLogClient") ReactiveElasticsearchClient esChangeLogClient) {
        return new DeltaDetectionService(esChangeLogClient);
    }

    @Bean(name = "deltaValueService")
    @ConditionalOnMissingBean(DeltaValueService.class)
    public DeltaValueService deltaService(MartDataPipeline martDataPipeline) {
        return new DeltaValueService(martDataPipeline);
    }

    @Bean(name = "NewTsGateway")
    @ConditionalOnMissingBean(TsGateway.class)
    public TsGateway tsGateway() {
        return new TsGateway();
    }

    @Bean
    @ConditionalOnMissingBean(ServiceLoggingAspect.class)
    public ServiceLoggingAspect serviceLoggingAspect() {
        return new ServiceLoggingAspect();
    }

    @Bean(name = "exchangeRateCache")
    @ConditionalOnMissingBean(name = "exchangeRateCache")
    public ExchangeRateCache getExchangeRateCache(ExchangeRateLoader exchangeRateLoader) {
        return new ExchangeRateCache(exchangeRateLoader, 500);
    }

    @Override
    public void afterPropertiesSet() throws Exception {

    }
}
