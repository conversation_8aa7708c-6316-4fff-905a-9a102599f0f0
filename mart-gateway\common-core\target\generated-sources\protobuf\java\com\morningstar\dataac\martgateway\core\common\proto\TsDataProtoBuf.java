// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: TsData.proto

// Protobuf Java Version: 3.25.1
package com.morningstar.dataac.martgateway.core.common.proto;

public final class TsDataProtoBuf {
  private TsDataProtoBuf() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface TSDataDoubleOrBuilder extends
      // @@protoc_insertion_point(interface_extends:protobuf.TSDataDouble)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string investmentId = 1;</code>
     * @return The investmentId.
     */
    java.lang.String getInvestmentId();
    /**
     * <code>string investmentId = 1;</code>
     * @return The bytes for investmentId.
     */
    com.google.protobuf.ByteString
        getInvestmentIdBytes();

    /**
     * <code>string dpId = 2;</code>
     * @return The dpId.
     */
    java.lang.String getDpId();
    /**
     * <code>string dpId = 2;</code>
     * @return The bytes for dpId.
     */
    com.google.protobuf.ByteString
        getDpIdBytes();

    /**
     * <code>repeated int64 dates = 3;</code>
     * @return A list containing the dates.
     */
    java.util.List<java.lang.Long> getDatesList();
    /**
     * <code>repeated int64 dates = 3;</code>
     * @return The count of dates.
     */
    int getDatesCount();
    /**
     * <code>repeated int64 dates = 3;</code>
     * @param index The index of the element to return.
     * @return The dates at the given index.
     */
    long getDates(int index);

    /**
     * <code>repeated double values = 4;</code>
     * @return A list containing the values.
     */
    java.util.List<java.lang.Double> getValuesList();
    /**
     * <code>repeated double values = 4;</code>
     * @return The count of values.
     */
    int getValuesCount();
    /**
     * <code>repeated double values = 4;</code>
     * @param index The index of the element to return.
     * @return The values at the given index.
     */
    double getValues(int index);

    /**
     * <code>repeated int32 copyOverDateIndices = 5;</code>
     * @return A list containing the copyOverDateIndices.
     */
    java.util.List<java.lang.Integer> getCopyOverDateIndicesList();
    /**
     * <code>repeated int32 copyOverDateIndices = 5;</code>
     * @return The count of copyOverDateIndices.
     */
    int getCopyOverDateIndicesCount();
    /**
     * <code>repeated int32 copyOverDateIndices = 5;</code>
     * @param index The index of the element to return.
     * @return The copyOverDateIndices at the given index.
     */
    int getCopyOverDateIndices(int index);
  }
  /**
   * Protobuf type {@code protobuf.TSDataDouble}
   */
  public static final class TSDataDouble extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:protobuf.TSDataDouble)
      TSDataDoubleOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TSDataDouble.newBuilder() to construct.
    private TSDataDouble(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TSDataDouble() {
      investmentId_ = "";
      dpId_ = "";
      dates_ = emptyLongList();
      values_ = emptyDoubleList();
      copyOverDateIndices_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TSDataDouble();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.internal_static_protobuf_TSDataDouble_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.internal_static_protobuf_TSDataDouble_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble.class, com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble.Builder.class);
    }

    public static final int INVESTMENTID_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object investmentId_ = "";
    /**
     * <code>string investmentId = 1;</code>
     * @return The investmentId.
     */
    @java.lang.Override
    public java.lang.String getInvestmentId() {
      java.lang.Object ref = investmentId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        investmentId_ = s;
        return s;
      }
    }
    /**
     * <code>string investmentId = 1;</code>
     * @return The bytes for investmentId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getInvestmentIdBytes() {
      java.lang.Object ref = investmentId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        investmentId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DPID_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object dpId_ = "";
    /**
     * <code>string dpId = 2;</code>
     * @return The dpId.
     */
    @java.lang.Override
    public java.lang.String getDpId() {
      java.lang.Object ref = dpId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        dpId_ = s;
        return s;
      }
    }
    /**
     * <code>string dpId = 2;</code>
     * @return The bytes for dpId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDpIdBytes() {
      java.lang.Object ref = dpId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        dpId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DATES_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.LongList dates_ =
        emptyLongList();
    /**
     * <code>repeated int64 dates = 3;</code>
     * @return A list containing the dates.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getDatesList() {
      return dates_;
    }
    /**
     * <code>repeated int64 dates = 3;</code>
     * @return The count of dates.
     */
    public int getDatesCount() {
      return dates_.size();
    }
    /**
     * <code>repeated int64 dates = 3;</code>
     * @param index The index of the element to return.
     * @return The dates at the given index.
     */
    public long getDates(int index) {
      return dates_.getLong(index);
    }
    private int datesMemoizedSerializedSize = -1;

    public static final int VALUES_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.DoubleList values_ =
        emptyDoubleList();
    /**
     * <code>repeated double values = 4;</code>
     * @return A list containing the values.
     */
    @java.lang.Override
    public java.util.List<java.lang.Double>
        getValuesList() {
      return values_;
    }
    /**
     * <code>repeated double values = 4;</code>
     * @return The count of values.
     */
    public int getValuesCount() {
      return values_.size();
    }
    /**
     * <code>repeated double values = 4;</code>
     * @param index The index of the element to return.
     * @return The values at the given index.
     */
    public double getValues(int index) {
      return values_.getDouble(index);
    }
    private int valuesMemoizedSerializedSize = -1;

    public static final int COPYOVERDATEINDICES_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList copyOverDateIndices_ =
        emptyIntList();
    /**
     * <code>repeated int32 copyOverDateIndices = 5;</code>
     * @return A list containing the copyOverDateIndices.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getCopyOverDateIndicesList() {
      return copyOverDateIndices_;
    }
    /**
     * <code>repeated int32 copyOverDateIndices = 5;</code>
     * @return The count of copyOverDateIndices.
     */
    public int getCopyOverDateIndicesCount() {
      return copyOverDateIndices_.size();
    }
    /**
     * <code>repeated int32 copyOverDateIndices = 5;</code>
     * @param index The index of the element to return.
     * @return The copyOverDateIndices at the given index.
     */
    public int getCopyOverDateIndices(int index) {
      return copyOverDateIndices_.getInt(index);
    }
    private int copyOverDateIndicesMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(investmentId_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, investmentId_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(dpId_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, dpId_);
      }
      if (getDatesList().size() > 0) {
        output.writeUInt32NoTag(26);
        output.writeUInt32NoTag(datesMemoizedSerializedSize);
      }
      for (int i = 0; i < dates_.size(); i++) {
        output.writeInt64NoTag(dates_.getLong(i));
      }
      if (getValuesList().size() > 0) {
        output.writeUInt32NoTag(34);
        output.writeUInt32NoTag(valuesMemoizedSerializedSize);
      }
      for (int i = 0; i < values_.size(); i++) {
        output.writeDoubleNoTag(values_.getDouble(i));
      }
      if (getCopyOverDateIndicesList().size() > 0) {
        output.writeUInt32NoTag(42);
        output.writeUInt32NoTag(copyOverDateIndicesMemoizedSerializedSize);
      }
      for (int i = 0; i < copyOverDateIndices_.size(); i++) {
        output.writeInt32NoTag(copyOverDateIndices_.getInt(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(investmentId_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, investmentId_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(dpId_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, dpId_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < dates_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(dates_.getLong(i));
        }
        size += dataSize;
        if (!getDatesList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        datesMemoizedSerializedSize = dataSize;
      }
      {
        int dataSize = 0;
        dataSize = 8 * getValuesList().size();
        size += dataSize;
        if (!getValuesList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        valuesMemoizedSerializedSize = dataSize;
      }
      {
        int dataSize = 0;
        for (int i = 0; i < copyOverDateIndices_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(copyOverDateIndices_.getInt(i));
        }
        size += dataSize;
        if (!getCopyOverDateIndicesList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        copyOverDateIndicesMemoizedSerializedSize = dataSize;
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble)) {
        return super.equals(obj);
      }
      com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble other = (com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble) obj;

      if (!getInvestmentId()
          .equals(other.getInvestmentId())) return false;
      if (!getDpId()
          .equals(other.getDpId())) return false;
      if (!getDatesList()
          .equals(other.getDatesList())) return false;
      if (!getValuesList()
          .equals(other.getValuesList())) return false;
      if (!getCopyOverDateIndicesList()
          .equals(other.getCopyOverDateIndicesList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + INVESTMENTID_FIELD_NUMBER;
      hash = (53 * hash) + getInvestmentId().hashCode();
      hash = (37 * hash) + DPID_FIELD_NUMBER;
      hash = (53 * hash) + getDpId().hashCode();
      if (getDatesCount() > 0) {
        hash = (37 * hash) + DATES_FIELD_NUMBER;
        hash = (53 * hash) + getDatesList().hashCode();
      }
      if (getValuesCount() > 0) {
        hash = (37 * hash) + VALUES_FIELD_NUMBER;
        hash = (53 * hash) + getValuesList().hashCode();
      }
      if (getCopyOverDateIndicesCount() > 0) {
        hash = (37 * hash) + COPYOVERDATEINDICES_FIELD_NUMBER;
        hash = (53 * hash) + getCopyOverDateIndicesList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protobuf.TSDataDouble}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:protobuf.TSDataDouble)
        com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDoubleOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.internal_static_protobuf_TSDataDouble_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.internal_static_protobuf_TSDataDouble_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble.class, com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble.Builder.class);
      }

      // Construct using com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        investmentId_ = "";
        dpId_ = "";
        dates_ = emptyLongList();
        values_ = emptyDoubleList();
        copyOverDateIndices_ = emptyIntList();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.internal_static_protobuf_TSDataDouble_descriptor;
      }

      @java.lang.Override
      public com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble getDefaultInstanceForType() {
        return com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble.getDefaultInstance();
      }

      @java.lang.Override
      public com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble build() {
        com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble buildPartial() {
        com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble result = new com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.investmentId_ = investmentId_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.dpId_ = dpId_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          dates_.makeImmutable();
          result.dates_ = dates_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          values_.makeImmutable();
          result.values_ = values_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          copyOverDateIndices_.makeImmutable();
          result.copyOverDateIndices_ = copyOverDateIndices_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble) {
          return mergeFrom((com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble other) {
        if (other == com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble.getDefaultInstance()) return this;
        if (!other.getInvestmentId().isEmpty()) {
          investmentId_ = other.investmentId_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (!other.getDpId().isEmpty()) {
          dpId_ = other.dpId_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        if (!other.dates_.isEmpty()) {
          if (dates_.isEmpty()) {
            dates_ = other.dates_;
            dates_.makeImmutable();
            bitField0_ |= 0x00000004;
          } else {
            ensureDatesIsMutable();
            dates_.addAll(other.dates_);
          }
          onChanged();
        }
        if (!other.values_.isEmpty()) {
          if (values_.isEmpty()) {
            values_ = other.values_;
            values_.makeImmutable();
            bitField0_ |= 0x00000008;
          } else {
            ensureValuesIsMutable();
            values_.addAll(other.values_);
          }
          onChanged();
        }
        if (!other.copyOverDateIndices_.isEmpty()) {
          if (copyOverDateIndices_.isEmpty()) {
            copyOverDateIndices_ = other.copyOverDateIndices_;
            copyOverDateIndices_.makeImmutable();
            bitField0_ |= 0x00000010;
          } else {
            ensureCopyOverDateIndicesIsMutable();
            copyOverDateIndices_.addAll(other.copyOverDateIndices_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                investmentId_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                dpId_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 24: {
                long v = input.readInt64();
                ensureDatesIsMutable();
                dates_.addLong(v);
                break;
              } // case 24
              case 26: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureDatesIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  dates_.addLong(input.readInt64());
                }
                input.popLimit(limit);
                break;
              } // case 26
              case 33: {
                double v = input.readDouble();
                ensureValuesIsMutable();
                values_.addDouble(v);
                break;
              } // case 33
              case 34: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                int alloc = length > 4096 ? 4096 : length;
                ensureValuesIsMutable(alloc / 8);
                while (input.getBytesUntilLimit() > 0) {
                  values_.addDouble(input.readDouble());
                }
                input.popLimit(limit);
                break;
              } // case 34
              case 40: {
                int v = input.readInt32();
                ensureCopyOverDateIndicesIsMutable();
                copyOverDateIndices_.addInt(v);
                break;
              } // case 40
              case 42: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureCopyOverDateIndicesIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  copyOverDateIndices_.addInt(input.readInt32());
                }
                input.popLimit(limit);
                break;
              } // case 42
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object investmentId_ = "";
      /**
       * <code>string investmentId = 1;</code>
       * @return The investmentId.
       */
      public java.lang.String getInvestmentId() {
        java.lang.Object ref = investmentId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          investmentId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string investmentId = 1;</code>
       * @return The bytes for investmentId.
       */
      public com.google.protobuf.ByteString
          getInvestmentIdBytes() {
        java.lang.Object ref = investmentId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          investmentId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string investmentId = 1;</code>
       * @param value The investmentId to set.
       * @return This builder for chaining.
       */
      public Builder setInvestmentId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        investmentId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string investmentId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearInvestmentId() {
        investmentId_ = getDefaultInstance().getInvestmentId();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string investmentId = 1;</code>
       * @param value The bytes for investmentId to set.
       * @return This builder for chaining.
       */
      public Builder setInvestmentIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        investmentId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private java.lang.Object dpId_ = "";
      /**
       * <code>string dpId = 2;</code>
       * @return The dpId.
       */
      public java.lang.String getDpId() {
        java.lang.Object ref = dpId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          dpId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string dpId = 2;</code>
       * @return The bytes for dpId.
       */
      public com.google.protobuf.ByteString
          getDpIdBytes() {
        java.lang.Object ref = dpId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          dpId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string dpId = 2;</code>
       * @param value The dpId to set.
       * @return This builder for chaining.
       */
      public Builder setDpId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        dpId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>string dpId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDpId() {
        dpId_ = getDefaultInstance().getDpId();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <code>string dpId = 2;</code>
       * @param value The bytes for dpId to set.
       * @return This builder for chaining.
       */
      public Builder setDpIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        dpId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.LongList dates_ = emptyLongList();
      private void ensureDatesIsMutable() {
        if (!dates_.isModifiable()) {
          dates_ = makeMutableCopy(dates_);
        }
        bitField0_ |= 0x00000004;
      }
      /**
       * <code>repeated int64 dates = 3;</code>
       * @return A list containing the dates.
       */
      public java.util.List<java.lang.Long>
          getDatesList() {
        dates_.makeImmutable();
        return dates_;
      }
      /**
       * <code>repeated int64 dates = 3;</code>
       * @return The count of dates.
       */
      public int getDatesCount() {
        return dates_.size();
      }
      /**
       * <code>repeated int64 dates = 3;</code>
       * @param index The index of the element to return.
       * @return The dates at the given index.
       */
      public long getDates(int index) {
        return dates_.getLong(index);
      }
      /**
       * <code>repeated int64 dates = 3;</code>
       * @param index The index to set the value at.
       * @param value The dates to set.
       * @return This builder for chaining.
       */
      public Builder setDates(
          int index, long value) {

        ensureDatesIsMutable();
        dates_.setLong(index, value);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 dates = 3;</code>
       * @param value The dates to add.
       * @return This builder for chaining.
       */
      public Builder addDates(long value) {

        ensureDatesIsMutable();
        dates_.addLong(value);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 dates = 3;</code>
       * @param values The dates to add.
       * @return This builder for chaining.
       */
      public Builder addAllDates(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureDatesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, dates_);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 dates = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearDates() {
        dates_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.DoubleList values_ = emptyDoubleList();
      private void ensureValuesIsMutable() {
        if (!values_.isModifiable()) {
          values_ = makeMutableCopy(values_);
        }
        bitField0_ |= 0x00000008;
      }
      private void ensureValuesIsMutable(int capacity) {
        if (!values_.isModifiable()) {
          values_ = makeMutableCopy(values_, capacity);
        }
        bitField0_ |= 0x00000008;
      }
      /**
       * <code>repeated double values = 4;</code>
       * @return A list containing the values.
       */
      public java.util.List<java.lang.Double>
          getValuesList() {
        values_.makeImmutable();
        return values_;
      }
      /**
       * <code>repeated double values = 4;</code>
       * @return The count of values.
       */
      public int getValuesCount() {
        return values_.size();
      }
      /**
       * <code>repeated double values = 4;</code>
       * @param index The index of the element to return.
       * @return The values at the given index.
       */
      public double getValues(int index) {
        return values_.getDouble(index);
      }
      /**
       * <code>repeated double values = 4;</code>
       * @param index The index to set the value at.
       * @param value The values to set.
       * @return This builder for chaining.
       */
      public Builder setValues(
          int index, double value) {

        ensureValuesIsMutable();
        values_.setDouble(index, value);
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>repeated double values = 4;</code>
       * @param value The values to add.
       * @return This builder for chaining.
       */
      public Builder addValues(double value) {

        ensureValuesIsMutable();
        values_.addDouble(value);
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>repeated double values = 4;</code>
       * @param values The values to add.
       * @return This builder for chaining.
       */
      public Builder addAllValues(
          java.lang.Iterable<? extends java.lang.Double> values) {
        ensureValuesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, values_);
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>repeated double values = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearValues() {
        values_ = emptyDoubleList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList copyOverDateIndices_ = emptyIntList();
      private void ensureCopyOverDateIndicesIsMutable() {
        if (!copyOverDateIndices_.isModifiable()) {
          copyOverDateIndices_ = makeMutableCopy(copyOverDateIndices_);
        }
        bitField0_ |= 0x00000010;
      }
      /**
       * <code>repeated int32 copyOverDateIndices = 5;</code>
       * @return A list containing the copyOverDateIndices.
       */
      public java.util.List<java.lang.Integer>
          getCopyOverDateIndicesList() {
        copyOverDateIndices_.makeImmutable();
        return copyOverDateIndices_;
      }
      /**
       * <code>repeated int32 copyOverDateIndices = 5;</code>
       * @return The count of copyOverDateIndices.
       */
      public int getCopyOverDateIndicesCount() {
        return copyOverDateIndices_.size();
      }
      /**
       * <code>repeated int32 copyOverDateIndices = 5;</code>
       * @param index The index of the element to return.
       * @return The copyOverDateIndices at the given index.
       */
      public int getCopyOverDateIndices(int index) {
        return copyOverDateIndices_.getInt(index);
      }
      /**
       * <code>repeated int32 copyOverDateIndices = 5;</code>
       * @param index The index to set the value at.
       * @param value The copyOverDateIndices to set.
       * @return This builder for chaining.
       */
      public Builder setCopyOverDateIndices(
          int index, int value) {

        ensureCopyOverDateIndicesIsMutable();
        copyOverDateIndices_.setInt(index, value);
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 copyOverDateIndices = 5;</code>
       * @param value The copyOverDateIndices to add.
       * @return This builder for chaining.
       */
      public Builder addCopyOverDateIndices(int value) {

        ensureCopyOverDateIndicesIsMutable();
        copyOverDateIndices_.addInt(value);
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 copyOverDateIndices = 5;</code>
       * @param values The copyOverDateIndices to add.
       * @return This builder for chaining.
       */
      public Builder addAllCopyOverDateIndices(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureCopyOverDateIndicesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, copyOverDateIndices_);
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 copyOverDateIndices = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearCopyOverDateIndices() {
        copyOverDateIndices_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:protobuf.TSDataDouble)
    }

    // @@protoc_insertion_point(class_scope:protobuf.TSDataDouble)
    private static final com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble();
    }

    public static com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TSDataDouble>
        PARSER = new com.google.protobuf.AbstractParser<TSDataDouble>() {
      @java.lang.Override
      public TSDataDouble parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<TSDataDouble> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TSDataDouble> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.morningstar.dataac.martgateway.core.common.proto.TsDataProtoBuf.TSDataDouble getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_protobuf_TSDataDouble_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_protobuf_TSDataDouble_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\014TsData.proto\022\010protobuf\"n\n\014TSDataDouble" +
      "\022\024\n\014investmentId\030\001 \001(\t\022\014\n\004dpId\030\002 \001(\t\022\r\n\005" +
      "dates\030\003 \003(\003\022\016\n\006values\030\004 \003(\001\022\033\n\023copyOverD" +
      "ateIndices\030\005 \003(\005BF\n4com.morningstar.data" +
      "ac.martgateway.core.common.protoB\016TsData" +
      "ProtoBufb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_protobuf_TSDataDouble_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_protobuf_TSDataDouble_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_protobuf_TSDataDouble_descriptor,
        new java.lang.String[] { "InvestmentId", "DpId", "Dates", "Values", "CopyOverDateIndices", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
